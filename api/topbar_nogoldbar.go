package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// Reuse TopBarCategory, CustomPage, Branch structs from previous top-bar code

// TopBarNoGoldbarHandler returns category, featured_category, custom_page, and branch excluding goldbar categories
func TopBarNoGoldbarHandler(db *gorm.DB) echo.HandlerFunc {
	return func(c echo.Context) error {
		tx := db.Begin()

		var category []TopBarCategory
		var featuredCategory []TopBarCategory
		var customPage []CustomPage
		var branch []Branch

		// Category excluding goldbar
		if err := tx.Table("setting_database").
			Where("kategori_Produk <> ? AND status = ? AND is_category_menu = ? AND is_publish = ? AND is_goldbar != ? AND img_path IS NOT NULL",
				"", 1, 1, 1, 1).
			Order("Kategori_Produk ASC").
			Select("id, Kategori_Produk, Kod_Kategori_Produk, img_path").
			Scan(&category).Error; err != nil {
			tx.Rollback()
			return c.J<PERSON>(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		// Featured category
		if err := tx.Table("setting_database").
			Where("kategori_Produk <> ? AND status = ? AND is_featured = ? AND is_publish = ? AND img_path IS NOT NULL",
				"", 1, 1, 1).
			Order("Kategori_Produk ASC").
			Select("id, Kategori_Produk, Kod_Kategori_Produk, img_path").
			Scan(&featuredCategory).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		// Custom page (no slug check here)
		if err := tx.Table("ecomm_custom_page").
			Where("status = ?", 1).
			Select("id, name").
			Scan(&customPage).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		// Branch
		if err := tx.Table("56_maklumat_kedai").
			Where("status = ? AND ecomm_is_display = ?", 1, 1).
			Select("cawangan").
			Scan(&branch).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, map[string]string{"error": err.Error()})
		}

		tx.Commit()

		return c.JSON(http.StatusOK, map[string]interface{}{
			"category":          category,
			"custom_page":       customPage,
			"featured_category": featuredCategory,
			"branch":            branch,
		})
	}
}
